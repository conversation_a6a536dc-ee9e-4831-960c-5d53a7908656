<!-- Modern Interactive Faculty Library Management Dashboard -->
<div class="faculty-dashboard min-h-screen transition-colors duration-300 flex" [class]="isDarkMode ? 'bg-gray-900' : 'bg-gray-50'" [attr.data-theme]="isDarkMode ? 'dark' : 'light'">
  <!-- Mobile Menu Button -->
  <button
    (click)="toggleMobileMenu()"
    class="lg:hidden fixed top-4 left-4 z-50 p-2 rounded-lg transition-colors duration-200"
    [class]="isDarkMode ? 'bg-gray-800 text-white hover:bg-gray-700' : 'bg-white text-gray-800 hover:bg-gray-100'"
  >
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
    </svg>
  </button>

  <!-- Mobile Overlay -->
  <div
    *ngIf="isMobileMenuOpen"
    class="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-30"
    (click)="closeMobileMenu()"
  ></div>

  <!-- Left Navigation Pane (Fixed) -->
  <aside
    class="w-64 shadow-lg flex-shrink-0 border-r flex flex-col transition-all duration-300"
    [class]="getAsideClasses()"
    [style.transform]="isMobileMenuOpen ? 'translateX(0)' : getDesktopTransform()"
    [style.position]="getAsidePosition()"
    [style.top]="'0'"
    [style.left]="'0'"
    [style.height]="'100vh'"
    [style.z-index]="getAsideZIndex()"
  >
    <!-- Header -->
    <div class="p-6 border-b transition-colors duration-300" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
      <div class="flex items-center justify-center">
        <img
          src="assets/images/BcLogo.png"
          alt="Benedicto College Logo"
          class="h-12 w-auto object-contain"
        >
      </div>
    </div>

    <!-- Navigation Links -->
    <nav class="flex-1 p-4 overflow-y-auto">
      <div class="space-y-2">
        <a (click)="onNavigate('dashboard'); closeMobileMenu()" [class]="getNavLinkClass('dashboard')" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer">
          <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
          </svg>
          Dashboard
        </a>
        <a (click)="onNavigate('borrow'); closeMobileMenu()" [class]="getNavLinkClass('borrow')" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer">
          <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
          Academic Resources
        </a>
        <a (click)="onNavigate('loans'); closeMobileMenu()" [class]="getNavLinkClass('loans')" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer">
          <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
          </svg>
          My Loans
        </a>
        <a (click)="onNavigate('course-materials'); closeMobileMenu()" [class]="getNavLinkClass('course-materials')" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer">
          <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
          </svg>
          Course Materials
        </a>
        <a (click)="onNavigate('research-tools'); closeMobileMenu()" [class]="getNavLinkClass('research-tools')" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer">
          <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Research Tools
        </a>
        <a (click)="onNavigate('reading-lists'); closeMobileMenu()" [class]="getNavLinkClass('reading-lists')" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer">
          <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
          </svg>
          Reading Lists
        </a>
        <a (click)="onNavigate('history'); closeMobileMenu()" [class]="getNavLinkClass('history')" class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer">
          <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          History
        </a>
      </div>
    </nav>

    <!-- Logout Button -->
    <div class="p-4 border-t transition-colors duration-300" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
      <button (click)="onLogout()" class="logout-btn w-full flex items-center justify-center px-4 py-3 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3v1"></path>
        </svg>
        Logout
      </button>
    </div>
  </aside>

  <!-- Main Content Area -->
  <div class="flex-1 flex main-content" [style.margin-left]="getMainContentMargin()">
    <!-- Dashboard Content -->
    <div class="flex-1 flex flex-col">
      <!-- Top Header -->
      <header [class]="getHeaderClasses()">
        <div class="flex justify-between items-center min-h-[64px]">
          <div class="flex-1 min-w-0">
            <h2 class="text-xl lg:text-2xl font-bold truncate" [class]="getTextClasses()">Faculty Dashboard</h2>
          </div>
          <div class="flex items-center space-x-2 lg:space-x-4 flex-shrink-0">
            <!-- Dark Mode Toggle -->
            <button
              (click)="toggleDarkMode()"
              class="p-2 rounded-lg transition-colors duration-200"
              [class]="isDarkMode ? 'text-yellow-400 hover:text-yellow-300' : 'text-gray-600 hover:text-gray-900'"
              title="Toggle Dark Mode"
            >
              <svg *ngIf="!isDarkMode" class="w-5 h-5 lg:w-6 lg:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 718.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
              </svg>
              <svg *ngIf="isDarkMode" class="w-5 h-5 lg:w-6 lg:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
            </button>
            <!-- Notification Bell -->
            <button
              (click)="onNotificationClick()"
              class="relative p-2 transition-colors duration-200"
              [class]="isDarkMode ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'"
            >
              <svg class="w-5 h-5 lg:w-6 lg:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
              </svg>
              <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white notification-badge"></span>
            </button>
            <!-- User Profile -->
            <div class="relative">
              <button
                (click)="onProfileClick()"
                class="flex items-center space-x-2 p-2 rounded-lg transition-colors duration-200"
                [class]="isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'"
              >
                <img class="w-7 h-7 lg:w-8 lg:h-8 rounded-full bg-green-500 flex items-center justify-center text-white text-sm font-medium" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Crect width='32' height='32' fill='%2310B981'/%3E%3Ctext x='16' y='20' text-anchor='middle' fill='white' font-family='Arial' font-size='14' font-weight='bold'%3EF%3C/text%3E%3C/svg%3E" alt="Faculty">
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- Main Dashboard Content -->
      <main class="flex-1 p-4 lg:p-6 overflow-y-auto transition-colors duration-300" [class]="getMainContentClasses()">
        <div class="max-w-7xl mx-auto">

          <!-- Dashboard View -->
          <div *ngIf="currentView === 'dashboard'">
            <!-- Quick Search Section -->
            <div class="mb-8">
              <h3 class="text-xl font-semibold mb-4" [class]="getTextClasses()">Quick Academic Search</h3>
              <div class="p-6 rounded-lg shadow-sm border" [class]="getCardClasses()">
                <div class="search-container flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                  <input
                    [(ngModel)]="searchQuery"
                    (keyup.enter)="onQuickSearch()"
                    type="text"
                    placeholder="Search for academic books, research materials, or journals..."
                    class="flex-1 px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 text-lg"
                    [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'"
                  >
                  <button
                    (click)="onQuickSearch()"
                    [disabled]="isSearching"
                    class="search-button px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200 font-medium w-full sm:w-auto"
                  >
                    <span *ngIf="!isSearching" class="flex items-center">
                      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                      </svg>
                      Search
                    </span>
                    <span *ngIf="isSearching" class="flex items-center">
                      <svg class="animate-spin w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Searching...
                    </span>
                  </button>
                </div>
              </div>
            </div>

          <!-- Faculty News Section -->
          <div class="mb-8">
            <h3 class="text-xl font-semibold mb-4" [class]="getTextClasses()">Faculty Announcements</h3>
            <div class="space-y-3">
              <div class="p-4 rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200" [class]="getCardClasses()">
                <div class="flex items-center">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                  <p [class]="getSecondaryTextClasses()">New research database access available.</p>
                </div>
              </div>
              <div class="p-4 rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200" [class]="getCardClasses()">
                <div class="flex items-center">
                  <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                  <p [class]="getSecondaryTextClasses()">Faculty development workshop on July 20.</p>
                </div>
              </div>
              <div class="p-4 rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200" [class]="getCardClasses()">
                <div class="flex items-center">
                  <div class="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                  <p [class]="getSecondaryTextClasses()">Extended loan periods for summer research.</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Faculty Quick Links Section -->
          <div class="mb-8">
            <h3 class="text-xl font-semibold mb-4" [class]="getTextClasses()">Faculty Quick Links</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <!-- Course Materials -->
              <div (click)="onQuickLink('course-materials')" class="p-4 rounded-lg shadow-sm border hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105" [class]="getCardClasses()">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-green-100 mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold" [class]="getTextClasses()">Course Materials</h4>
                    <p class="text-sm" [class]="getSecondaryTextClasses()">Manage class resources</p>
                  </div>
                </div>
              </div>

              <!-- Research Tools -->
              <div (click)="onQuickLink('research-tools')" class="p-4 rounded-lg shadow-sm border hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105" [class]="getCardClasses()">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-purple-100 mr-4">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold" [class]="getTextClasses()">Research Tools</h4>
                    <p class="text-sm" [class]="getSecondaryTextClasses()">Access databases</p>
                  </div>
                </div>
              </div>

              <!-- Reading Lists -->
              <div (click)="onQuickLink('reading-lists')" class="p-4 rounded-lg shadow-sm border hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105" [class]="getCardClasses()">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-indigo-100 mr-4">
                    <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold" [class]="getTextClasses()">Reading Lists</h4>
                    <p class="text-sm" [class]="getSecondaryTextClasses()">Create course lists</p>
                  </div>
                </div>
              </div>

              <!-- Digital Library -->
              <div (click)="onQuickLink('digital-library')" class="p-4 rounded-lg shadow-sm border hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105" [class]="getCardClasses()">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-yellow-100 mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold" [class]="getTextClasses()">Digital Library</h4>
                    <p class="text-sm" [class]="getSecondaryTextClasses()">Online resources</p>
                  </div>
                </div>
              </div>

              <!-- Faculty Support -->
              <div (click)="onQuickLink('faculty-support')" class="p-4 rounded-lg shadow-sm border hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105" [class]="getCardClasses()">
                <div class="flex items-center">
                  <div class="p-3 rounded-full bg-red-100 mr-4">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-semibold" [class]="getTextClasses()">Faculty Support</h4>
                    <p class="text-sm" [class]="getSecondaryTextClasses()">Get assistance</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Faculty Stats Section -->
          <div class="mb-8">
            <h3 class="text-xl font-semibold mb-4" [class]="getTextClasses()">Faculty Overview</h3>
            <div class="p-6 rounded-lg shadow-sm border transition-shadow duration-200" [class]="getCardClasses()">
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-blue-600" id="courses-count">{{ facultyStats.activeClasses }}</div>
                  <div class="text-sm" [class]="getSecondaryTextClasses()">Active Courses</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600" id="materials-count">{{ facultyStats.courseMaterials }}</div>
                  <div class="text-sm" [class]="getSecondaryTextClasses()">Course Materials</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-purple-600" id="research-count">{{ facultyStats.researchProjects }}</div>
                  <div class="text-sm" [class]="getSecondaryTextClasses()">Research Projects</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-orange-600" id="borrowed-count">{{ facultyStats.borrowed }}</div>
                  <div class="text-sm" [class]="getSecondaryTextClasses()">Books Borrowed</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Widgets Section - Only visible on mobile -->
          <div class="lg:hidden space-y-6">
            <!-- Weather Widget -->
            <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-lg font-semibold mb-1">Weather</h4>
                  <p class="text-3xl font-bold" id="temperature-mobile">{{ temperature }}</p>
                  <p class="text-blue-100" id="location-mobile">{{ location }}</p>
                </div>
                <div class="text-right">
                  <svg class="w-12 h-12 text-yellow-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="weather-icon-mobile">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Horoscope Widget -->
            <div class="border rounded-lg p-6 transition-colors duration-300" [class]="getCardClasses()">
              <div class="flex items-center mb-3">
                <svg class="w-6 h-6 text-purple-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                <h4 class="text-lg font-semibold" [class]="getTextClasses()">Horoscope</h4>
              </div>
              <p [class]="getSecondaryTextClasses()"><strong>Cancer:</strong> Study focus brings good results.</p>
            </div>

            <!-- Calendar Events Widget -->
            <div class="border rounded-lg p-6 transition-colors duration-300" [class]="getCardClasses()">
              <div class="flex items-center mb-4">
                <svg class="w-6 h-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <h4 class="text-lg font-semibold" [class]="getTextClasses()">Upcoming Events</h4>
              </div>
              <div class="space-y-3">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <div>
                    <p class="font-medium" [class]="getTextClasses()">July 10 – Book Fair</p>
                    <p class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">3 days remaining</p>
                  </div>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-orange-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <div>
                    <p class="font-medium" [class]="getTextClasses()">July 15 – Research Submission</p>
                    <p class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">8 days remaining</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Quote of the Day Widget -->
            <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg p-6 text-white">
              <div class="flex items-center mb-3">
                <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
                </svg>
                <h4 class="text-lg font-semibold">Quote of the Day</h4>
              </div>
              <p class="text-purple-100 italic">"A room without books is like a body without a soul."</p>
            </div>

            <!-- Faculty Stats Widget -->
            <div class="border rounded-lg p-6 transition-colors duration-300" [class]="getCardClasses()">
              <h4 class="text-lg font-semibold mb-4" [class]="getTextClasses()">My Faculty Stats</h4>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <svg class="w-6 h-6 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    <span [class]="getSecondaryTextClasses()">Active Courses</span>
                  </div>
                  <span class="text-xl font-bold" [class]="getTextClasses()" id="courses-count-mobile">{{ facultyStats.activeClasses }}</span>
                </div>
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <svg class="w-6 h-6 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <span [class]="getSecondaryTextClasses()">Books Borrowed</span>
                  </div>
                  <span class="text-xl font-bold" [class]="getTextClasses()" id="borrowed-count-mobile">{{ facultyStats.borrowed }}</span>
                </div>
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <svg class="w-6 h-6 text-purple-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span [class]="getSecondaryTextClasses()">Research Projects</span>
                  </div>
                  <span class="text-xl font-bold" [class]="getTextClasses()" id="research-count-mobile">{{ facultyStats.researchProjects }}</span>
                </div>
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <svg class="w-6 h-6 text-orange-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    <span [class]="getSecondaryTextClasses()">Course Materials</span>
                  </div>
                  <span class="text-xl font-bold" [class]="getTextClasses()" id="materials-count-mobile">{{ facultyStats.courseMaterials }}</span>
                </div>
              </div>
            </div>
          </div>
          </div>

          <!-- Academic Resources View -->
          <div *ngIf="currentView === 'borrow'">
            <div class="mb-6">
              <h2 class="text-2xl font-bold mb-4" [class]="getTextClasses()">Academic Resources</h2>

              <!-- Search and Filter Controls -->
              <div class="mb-6 p-4 rounded-lg border" [class]="getCardClasses()">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <input
                    [(ngModel)]="searchFilter"
                    type="text"
                    placeholder="Search academic resources..."
                    class="px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                    [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'"
                  >
                  <select
                    [(ngModel)]="categoryFilter"
                    class="px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                    [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'"
                  >
                    <option value="all">All Categories</option>
                    <option value="Computer Science">Computer Science</option>
                    <option value="Education">Education</option>
                    <option value="Mathematics">Mathematics</option>
                    <option value="Research Methods">Research Methods</option>
                    <option value="Psychology">Psychology</option>
                  </select>
                  <select
                    [(ngModel)]="statusFilter"
                    class="px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
                    [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'"
                  >
                    <option value="all">All Status</option>
                    <option value="Available">Available</option>
                    <option value="Checked Out">Checked Out</option>
                    <option value="Reserved">Reserved</option>
                  </select>
                </div>
              </div>

              <!-- Books Grid -->
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div *ngFor="let book of paginatedItems" class="border rounded-lg p-6 hover:shadow-lg transition-shadow" [class]="getCardClasses()">
                  <div class="mb-4">
                    <h3 class="text-lg font-semibold mb-2" [class]="getTextClasses()">{{ book.title }}</h3>
                    <p class="text-sm mb-1" [class]="getSecondaryTextClasses()">by {{ book.author }}</p>
                    <p class="text-sm mb-2" [class]="getSecondaryTextClasses()">{{ book.category }}</p>
                    <span class="inline-block px-2 py-1 text-xs rounded-full" [class]="getAvailabilityClass(book.availability)">
                      {{ book.availability }}
                    </span>
                  </div>
                  <div class="mb-4">
                    <p class="text-sm" [class]="getSecondaryTextClasses()">
                      <strong>Location:</strong> {{ book.location }}
                    </p>
                    <p class="text-sm" [class]="getSecondaryTextClasses()">
                      <strong>ISBN:</strong> {{ book.isbn }}
                    </p>
                    <p *ngIf="book.description" class="text-sm mt-2" [class]="getSecondaryTextClasses()">
                      {{ book.description }}
                    </p>
                  </div>
                  <div class="flex space-x-2">
                    <button
                      *ngIf="book.availability === 'Available'"
                      (click)="borrowBook(book.id)"
                      class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Borrow
                    </button>
                    <button
                      *ngIf="book.availability === 'Checked Out'"
                      (click)="reserveBook(book.id)"
                      class="flex-1 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
                    >
                      Reserve
                    </button>
                    <button
                      *ngIf="book.availability === 'Reserved'"
                      disabled
                      class="flex-1 px-4 py-2 bg-gray-400 text-white rounded-lg cursor-not-allowed"
                    >
                      Reserved
                    </button>
                  </div>
                </div>
              </div>

              <!-- Pagination -->
              <div *ngIf="totalPages > 1" class="mt-6 flex justify-center">
                <nav class="flex space-x-2">
                  <button
                    (click)="changePage(currentPage - 1)"
                    [disabled]="currentPage === 1"
                    class="px-3 py-2 border rounded-lg disabled:opacity-50"
                    [class]="isDarkMode ? 'border-gray-600 text-gray-300' : 'border-gray-300 text-gray-700'"
                  >
                    Previous
                  </button>
                  <span class="px-3 py-2" [class]="getTextClasses()">
                    Page {{ currentPage }} of {{ totalPages }}
                  </span>
                  <button
                    (click)="changePage(currentPage + 1)"
                    [disabled]="currentPage === totalPages"
                    class="px-3 py-2 border rounded-lg disabled:opacity-50"
                    [class]="isDarkMode ? 'border-gray-600 text-gray-300' : 'border-gray-300 text-gray-700'"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>

          <!-- My Loans View -->
          <div *ngIf="currentView === 'loans'">
            <div class="mb-6">
              <h2 class="text-2xl font-bold mb-4" [class]="getTextClasses()">My Loans</h2>

              <!-- Loans Table -->
              <div class="overflow-x-auto">
                <table class="w-full border rounded-lg" [class]="getCardClasses()">
                  <thead class="border-b" [class]="isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Book</th>
                      <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Borrowed</th>
                      <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Due Date</th>
                      <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Status</th>
                      <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Actions</th>
                    </tr>
                  </thead>
                  <tbody class="divide-y" [class]="isDarkMode ? 'divide-gray-700' : 'divide-gray-200'">
                    <tr *ngFor="let loan of paginatedItems" class="hover:bg-opacity-50" [class]="isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'">
                      <td class="px-6 py-4">
                        <div>
                          <div class="text-sm font-medium" [class]="getTextClasses()">{{ loan.book.title }}</div>
                          <div class="text-sm" [class]="getSecondaryTextClasses()">by {{ loan.book.author }}</div>
                        </div>
                      </td>
                      <td class="px-6 py-4 text-sm" [class]="getSecondaryTextClasses()">
                        {{ loan.borrowDate | date:'MMM d, y' }}
                      </td>
                      <td class="px-6 py-4 text-sm">
                        <div [class]="loan.status === 'Overdue' ? 'text-red-600' : getSecondaryTextClasses()">
                          {{ loan.dueDate | date:'MMM d, y' }}
                          <div *ngIf="loan.status === 'Overdue'" class="text-xs text-red-600">
                            {{ getDaysUntilDue(loan.dueDate) * -1 }} days overdue
                          </div>
                          <div *ngIf="loan.status === 'Active' && getDaysUntilDue(loan.dueDate) <= 7" class="text-xs text-yellow-600">
                            Due in {{ getDaysUntilDue(loan.dueDate) }} days
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" [class]="getStatusClass(loan.status)">
                          {{ loan.status }}
                        </span>
                      </td>
                      <td class="px-6 py-4 text-sm">
                        <button
                          *ngIf="loan.renewalCount < loan.maxRenewals && loan.status === 'Active'"
                          (click)="renewLoan(loan.id)"
                          class="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors mr-2"
                        >
                          Renew ({{ loan.renewalCount }}/{{ loan.maxRenewals }})
                        </button>
                        <span *ngIf="loan.renewalCount >= loan.maxRenewals" class="text-xs" [class]="getSecondaryTextClasses()">
                          Max renewals reached
                        </span>
                        <span *ngIf="loan.fineAmount" class="text-xs text-red-600 block">
                          Fine: ₱{{ loan.fineAmount }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Course Materials View -->
          <div *ngIf="currentView === 'course-materials'">
            <div class="mb-6">
              <h2 class="text-2xl font-bold mb-4" [class]="getTextClasses()">Course Materials</h2>
              <div class="p-6 rounded-lg border" [class]="getCardClasses()">
                <p [class]="getSecondaryTextClasses()">Course materials management will be implemented here. Faculty can organize reading lists, upload course documents, and manage class resources.</p>
              </div>
            </div>
          </div>

          <!-- Research Tools View -->
          <div *ngIf="currentView === 'research-tools'">
            <div class="mb-6">
              <h2 class="text-2xl font-bold mb-4" [class]="getTextClasses()">Research Tools</h2>
              <div class="p-6 rounded-lg border" [class]="getCardClasses()">
                <p [class]="getSecondaryTextClasses()">Research tools and database access will be implemented here. Faculty can access academic databases, citation tools, and research resources.</p>
              </div>
            </div>
          </div>

          <!-- Reading Lists View -->
          <div *ngIf="currentView === 'reading-lists'">
            <div class="mb-6">
              <h2 class="text-2xl font-bold mb-4" [class]="getTextClasses()">Reading Lists</h2>
              <div class="p-6 rounded-lg border" [class]="getCardClasses()">
                <p [class]="getSecondaryTextClasses()">Reading list creation and management will be implemented here. Faculty can create, edit, and share reading lists for their courses.</p>
              </div>
            </div>
          </div>

          <!-- History View -->
          <div *ngIf="currentView === 'history'">
            <div class="mb-6">
              <h2 class="text-2xl font-bold mb-4" [class]="getTextClasses()">Borrowing History</h2>

              <!-- History Table -->
              <div class="overflow-x-auto">
                <table class="w-full border rounded-lg" [class]="getCardClasses()">
                  <thead class="border-b" [class]="isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Book</th>
                      <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Borrowed</th>
                      <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Due Date</th>
                      <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Returned</th>
                      <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" [class]="getSecondaryTextClasses()">Status</th>
                    </tr>
                  </thead>
                  <tbody class="divide-y" [class]="isDarkMode ? 'divide-gray-700' : 'divide-gray-200'">
                    <tr *ngFor="let loan of paginatedItems" class="hover:bg-opacity-50" [class]="isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'">
                      <td class="px-6 py-4">
                        <div>
                          <div class="text-sm font-medium" [class]="getTextClasses()">{{ loan.book.title }}</div>
                          <div class="text-sm" [class]="getSecondaryTextClasses()">by {{ loan.book.author }}</div>
                        </div>
                      </td>
                      <td class="px-6 py-4 text-sm" [class]="getSecondaryTextClasses()">
                        {{ loan.borrowDate | date:'MMM d, y' }}
                      </td>
                      <td class="px-6 py-4 text-sm" [class]="getSecondaryTextClasses()">
                        {{ loan.dueDate | date:'MMM d, y' }}
                      </td>
                      <td class="px-6 py-4 text-sm" [class]="getSecondaryTextClasses()">
                        {{ loan.returnDate | date:'MMM d, y' }}
                      </td>
                      <td class="px-6 py-4">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" [class]="getStatusClass(loan.status)">
                          {{ loan.status }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

        </div>
      </main>
    </div>

    <!-- Right Sidebar (Hidden on mobile, visible on desktop) -->
    <aside class="right-sidebar w-80 shadow-lg border-l flex-shrink-0 transition-colors duration-300 lg:block" [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'"
           [style.display]="isDesktop() ? 'block' : 'none'">
      <div class="p-6 space-y-6">
        <!-- Weather Widget -->
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-lg font-semibold mb-1">Weather</h4>
              <p class="text-3xl font-bold" id="temperature">{{ temperature }}</p>
              <p class="text-blue-100" id="location">{{ location }}</p>
            </div>
            <div class="text-right">
              <svg class="w-12 h-12 text-yellow-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="weather-icon">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"/>
              </svg>
            </div>
          </div>
        </div>

        <!-- Horoscope Widget -->
        <div class="border rounded-lg p-6 transition-colors duration-300" [class]="getCardClasses()">
          <div class="flex items-center mb-3">
            <svg class="w-6 h-6 text-purple-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <h4 class="text-lg font-semibold" [class]="getTextClasses()">Horoscope</h4>
          </div>
          <p [class]="getSecondaryTextClasses()"><strong>Cancer:</strong> Study focus brings good results.</p>
        </div>

        <!-- Calendar Events Widget -->
        <div class="border rounded-lg p-6 transition-colors duration-300" [class]="getCardClasses()">
          <div class="flex items-center mb-4">
            <svg class="w-6 h-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <h4 class="text-lg font-semibold" [class]="getTextClasses()">Upcoming Events</h4>
          </div>
          <div class="space-y-3">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <div>
                <p class="font-medium" [class]="getTextClasses()">July 10 – Book Fair</p>
                <p class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">3 days remaining</p>
              </div>
            </div>
            <div class="flex items-center">
              <svg class="w-5 h-5 text-orange-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <div>
                <p class="font-medium" [class]="getTextClasses()">July 15 – Research Submission</p>
                <p class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">8 days remaining</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Quote of the Day Widget -->
        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg p-6 text-white">
          <div class="flex items-center mb-3">
            <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
            </svg>
            <h4 class="text-lg font-semibold">Quote of the Day</h4>
          </div>
          <p class="text-purple-100 italic">"A room without books is like a body without a soul."</p>
        </div>

        <!-- Faculty Stats Widget -->
        <div class="border rounded-lg p-6 transition-colors duration-300" [class]="getCardClasses()">
          <h4 class="text-lg font-semibold mb-4" [class]="getTextClasses()">My Faculty Stats</h4>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <svg class="w-6 h-6 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <span [class]="getSecondaryTextClasses()">Active Courses</span>
              </div>
              <span class="text-xl font-bold" [class]="getTextClasses()" id="courses-count-sidebar">{{ facultyStats.activeClasses }}</span>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <svg class="w-6 h-6 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
                <span [class]="getSecondaryTextClasses()">Books Borrowed</span>
              </div>
              <span class="text-xl font-bold" [class]="getTextClasses()" id="borrowed-count-sidebar">{{ facultyStats.borrowed }}</span>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <svg class="w-6 h-6 text-purple-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span [class]="getSecondaryTextClasses()">Research Projects</span>
              </div>
              <span class="text-xl font-bold" [class]="getTextClasses()" id="research-count-sidebar">{{ facultyStats.researchProjects }}</span>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <svg class="w-6 h-6 text-orange-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <span [class]="getSecondaryTextClasses()">Course Materials</span>
              </div>
              <span class="text-xl font-bold" [class]="getTextClasses()" id="materials-count-sidebar">{{ facultyStats.courseMaterials }}</span>
            </div>
          </div>
        </div>
      </div>
    </aside>
  </div>

  <!-- BC-AI Chat Widget -->
  <div class="fixed bottom-6 right-6 z-50">
    <!-- Chat Toggle Button -->
    <div class="relative">
      <button
        (click)="toggleChat()"
        (mouseenter)="showTooltip = true"
        (mouseleave)="showTooltip = false"
        class="w-14 h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg transition-all duration-300 flex items-center justify-center chat-toggle-btn"
        [class.animate-pulse]="hasUnreadMessages"
      >
        <svg *ngIf="!isChatOpen" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
        <svg *ngIf="isChatOpen" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>

        <!-- Unread Messages Badge -->
        <span *ngIf="hasUnreadMessages && !isChatOpen" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">
          {{ unreadCount }}
        </span>
      </button>

      <!-- Tooltip -->
      <div *ngIf="showTooltip && !isChatOpen" class="absolute bottom-16 right-0 bg-gray-800 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap tooltip">
        Need help finding academic resources?
        <div class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
      </div>
    </div>

    <!-- Chat Window -->
    <div *ngIf="isChatOpen" class="absolute bottom-16 right-0 w-80 h-96 bg-white rounded-lg shadow-xl border flex flex-col chat-window" [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'">
      <!-- Chat Header -->
      <div class="p-4 border-b rounded-t-lg" [class]="isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-blue-600 border-blue-700'">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
            <span class="text-white text-sm font-bold">BC</span>
          </div>
          <div>
            <h4 class="font-semibold text-white">BC-AI Assistant</h4>
            <p class="text-xs text-blue-100">Faculty Library Support</p>
          </div>
        </div>
      </div>

      <!-- Chat Messages -->
      <div #chatMessagesContainer class="flex-1 p-4 overflow-y-auto space-y-3 chat-messages">
        <div *ngFor="let message of chatMessages" class="flex" [class.justify-end]="message.isUser">
          <div class="max-w-xs px-3 py-2 rounded-lg" [class]="message.isUser ? 'bg-blue-600 text-white' : (isDarkMode ? 'bg-gray-700 text-white' : 'bg-gray-200 text-gray-800')">
            <p class="text-sm">{{ message.text }}</p>
            <p class="text-xs mt-1 opacity-70">{{ message.timestamp | date:'short' }}</p>
          </div>
        </div>

        <!-- Typing Indicator -->
        <div *ngIf="isTyping" class="flex">
          <div class="max-w-xs px-3 py-2 rounded-lg" [class]="isDarkMode ? 'bg-gray-700 text-white' : 'bg-gray-200 text-gray-800'">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chat Input -->
      <div class="p-4 border-t" [class]="isDarkMode ? 'border-gray-600' : 'border-gray-200'">
        <div class="flex space-x-2">
          <input
            [(ngModel)]="chatInput"
            (keyup.enter)="sendMessage()"
            type="text"
            placeholder="Ask about faculty resources..."
            class="flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'"
          >
          <button
            (click)="sendMessage()"
            [disabled]="!chatInput.trim()"
            class="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Logout Confirmation Modal -->
  <div *ngIf="showLogoutModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="rounded-lg p-6 max-w-sm w-full mx-4 transition-colors duration-300" [class]="isDarkMode ? 'bg-gray-800' : 'bg-white'">
      <h3 class="text-lg font-medium mb-4" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Confirm Logout</h3>
      <p class="text-sm mb-6" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-600'">Are you sure you want to log out?</p>
      <div class="flex space-x-3">
        <button (click)="cancelLogout()" class="flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200" [class]="isDarkMode ? 'bg-gray-700 text-white hover:bg-gray-600' : 'bg-gray-200 text-gray-900 hover:bg-gray-300'">
          Cancel
        </button>
        <button (click)="confirmLogout()" class="flex-1 px-4 py-2 text-sm font-medium rounded-md bg-red-600 text-white hover:bg-red-700 transition-colors duration-200">
          Logout
        </button>
      </div>
    </div>
  </div>
</div>
