/* Faculty Dashboard Specific Styles */

/* Navigation Links */
.nav-link {
  @apply flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer;
}

.nav-link:hover {
  @apply bg-gray-100 text-gray-900;
}

.nav-link.active {
  @apply bg-blue-600 text-white;
}

/* Dark mode navigation */
[data-theme="dark"] .nav-link:hover {
  @apply bg-gray-700 text-white;
}

[data-theme="dark"] .nav-link.active {
  @apply bg-blue-600 text-white;
}

/* Search Button Animation */
.search-button {
  position: relative;
  overflow: hidden;
}

.search-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.search-button:hover::before {
  left: 100%;
}

/* Logout <PERSON> */
.logout-btn {
  position: relative;
  overflow: hidden;
}

.logout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.logout-btn:hover::before {
  left: 100%;
}

/* Chat Widget Styles */
.chat-toggle-btn {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.chat-toggle-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.5);
}

.chat-window {
  animation: slideUp 0.3s ease-out;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-messages {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Dark mode chat scrollbar */
[data-theme="dark"] .chat-messages {
  scrollbar-color: #4a5568 #2d3748;
}

[data-theme="dark"] .chat-messages::-webkit-scrollbar-track {
  background: #2d3748;
}

[data-theme="dark"] .chat-messages::-webkit-scrollbar-thumb {
  background: #4a5568;
}

[data-theme="dark"] .chat-messages::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* Tooltip */
.tooltip {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Notification Badge */
.notification-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Right Sidebar */
.right-sidebar {
  min-height: 100vh;
}

/* Mobile Responsive */
@media (max-width: 1023px) {
  /* Make left sidebar mobile-friendly */
  aside.w-64 {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 40;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  /* Show left sidebar when mobile menu is open */
  aside.w-64[style*="translateX(0)"] {
    transform: translateX(0) !important;
  }

  /* Hide right sidebar on mobile */
  .right-sidebar {
    display: none;
  }

  /* Adjust main content for mobile */
  .main-content {
    margin-left: 0;
  }

  /* Mobile padding adjustments */
  main {
    padding: 1rem;
  }

  /* Mobile table scroll */
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile grid adjustments */
  .grid {
    gap: 1rem;
  }
}

/* Desktop Layout Fix */
@media (min-width: 1024px) {
  /* Fix sidebar positioning on desktop */
  aside.w-64 {
    position: fixed !important;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 30;
  }

  /* Adjust main content to account for fixed sidebar */
  .main-content {
    margin-left: 16rem; /* 256px = w-64 */
  }
}

/* Sidebar Layout Fix */
aside.w-64 {
  min-height: 100vh;
}

/* Ensure navigation area can scroll if needed */
aside.w-64 nav {
  min-height: 0; /* Allow flex child to shrink */
}

/* Desktop styles */
@media (min-width: 1024px) {
  /* Hide mobile menu button on desktop */
  .lg\:hidden {
    display: none;
  }

  /* Ensure sidebar is visible on desktop */
  aside.w-64 {
    position: relative;
    transform: translateX(0) !important;
  }
}

/* Card Hover Effects */
.hover\:scale-105:hover {
  transform: scale(1.05);
}

/* Faculty-specific color scheme */
.faculty-primary {
  @apply bg-green-600 text-white;
}

.faculty-primary:hover {
  @apply bg-green-700;
}

.faculty-secondary {
  @apply bg-green-100 text-green-800;
}

/* Academic resource cards */
.academic-card {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.academic-card:hover {
  border-left-color: #10b981;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Faculty stats animation */
.stat-counter {
  animation: countUp 1s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Dark mode loading shimmer */
[data-theme="dark"] .loading-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

/* Faculty badge styles */
.faculty-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800;
}

[data-theme="dark"] .faculty-badge {
  @apply bg-green-900 text-green-200;
}

/* Enhanced focus states for accessibility */
.focus\:ring-green-500:focus {
  --tw-ring-color: #10b981;
}

/* Custom scrollbar for main content */
.main-content {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 transparent;
}

.main-content::-webkit-scrollbar {
  width: 8px;
}

.main-content::-webkit-scrollbar-track {
  background: transparent;
}

.main-content::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Dark mode main content scrollbar */
[data-theme="dark"] .main-content {
  scrollbar-color: #4a5568 transparent;
}

[data-theme="dark"] .main-content::-webkit-scrollbar-thumb {
  background: #4a5568;
}

[data-theme="dark"] .main-content::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* Faculty-specific animations */
.faculty-entrance {
  animation: facultySlideIn 0.6s ease-out;
}

@keyframes facultySlideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Research tools highlight */
.research-highlight {
  position: relative;
}

.research-highlight::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(16, 185, 129, 0.1), rgba(59, 130, 246, 0.1));
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.research-highlight:hover::before {
  opacity: 1;
}
